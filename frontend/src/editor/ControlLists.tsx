import AudioFileIcon from "@mui/icons-material/AudioFile";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import ColorLensIcon from "@mui/icons-material/ColorLens";
import ImageIcon from "@mui/icons-material/Image";
import StyleIcon from "@mui/icons-material/Style";
import VideocamIcon from "@mui/icons-material/Videocam";
import TextFieldsIcon from "@mui/icons-material/TextFields";
import MusicNoteIcon from "@mui/icons-material/MusicNote";
import VideoFileIcon from "@mui/icons-material/VideoFile";
import WysiwygIcon from "@mui/icons-material/Wysiwyg";
import ShapeLineIcon from "@mui/icons-material/ShapeLine";
import CategoryIcon from "@mui/icons-material/Category";
import SubtitlesIcon from "@mui/icons-material/Subtitles";
import { Box, IconButton, Paper } from "@mui/material";
import { observer } from "mobx-react-lite";
import React, { useCallback, useEffect, useState } from "react";
import { StoreContext } from "../store";
import { useLayoutStore } from "../store/store-context";
import AnimationOutlinedIcon from "@mui/icons-material/AnimationOutlined";
// Add type definitions
type ControlType =
  | "image"
  | "video"
  | "audio"
  | "text"
  | "canvas"
  | "shape"
  | "caption";

type MenuListProps = {
  openToolboxItem: (type: string) => void;
  type: ControlType;
};

// Simplified ControlList component
const ControlList = observer(() => {
  const store = React.useContext(StoreContext);
  const [controlType, setControlType] = useState<ControlType | null>(null);

  useEffect(() => {
    const selectedCaption = store.getSelectedCaption();

    if (store.selectedElement) {
      setControlType(store.selectedElement.type as ControlType);
    } else if (selectedCaption) {
      setControlType("caption");
    } else {
      setControlType(null);
    }
  }, [store.selectedElement, store.captions]);

  return controlType ? <ControlMenu controlType={controlType} /> : null;
});

const menuConfigs = {
  canvas: [{ type: "canvas", icon: <ColorLensIcon />, toolboxType: "canvas" }],
  image: [
    { type: "basic", icon: <ImageIcon />, toolboxType: "basic-image" },
    {
      type: "animation",
      icon: <AnimationOutlinedIcon />,
      toolboxType: "animation",
    },
    { type: "smart", icon: <AutoFixHighIcon />, toolboxType: "smart" },
  ],
  text: [
    { type: "basic", icon: <TextFieldsIcon />, toolboxType: "basic-text" },
    { type: "preset", icon: <WysiwygIcon />, toolboxType: "preset-text" },
    {
      type: "animation",
      icon: <AnimationOutlinedIcon />,
      toolboxType: "animation",
    },
    { type: "smart", icon: <AutoFixHighIcon />, toolboxType: "smart" },
  ],
  video: [
    { type: "basic", icon: <VideocamIcon />, toolboxType: "basic-video" },
    {
      type: "animation",
      icon: <AnimationOutlinedIcon />,
      toolboxType: "animation",
    },
  ],
  audio: [
    { type: "basic", icon: <MusicNoteIcon />, toolboxType: "basic-audio" },
    { type: "smart", icon: <AutoFixHighIcon />, toolboxType: "smart" },
  ],
  shape: [
    { type: "basic", icon: <CategoryIcon />, toolboxType: "basic-shape" },
    {
      type: "animation",
      icon: <AnimationOutlinedIcon />,
      toolboxType: "animation",
    },
  ],
  caption: [
    { type: "text", icon: <SubtitlesIcon />, toolboxType: "caption-text" },
    { type: "preset", icon: <WysiwygIcon />, toolboxType: "preset-caption" },
  ],
} as const;

// 创建通用的菜单列表组件
const MenuList = ({ type, openToolboxItem }: MenuListProps) => {
  const menuItems = menuConfigs[type];

  return (
    <BaseMenuList>
      {menuItems.map((item, index) => (
        <ToolboxIconButton
          key={index}
          onClick={() => openToolboxItem(item.toolboxType)}
          toolboxType={item.toolboxType}
        >
          {item.icon}
        </ToolboxIconButton>
      ))}
    </BaseMenuList>
  );
};

// Simplified ControlMenu component
const ControlMenu = observer(
  ({ controlType }: { controlType: ControlType }) => {
    const layoutStore = useLayoutStore();

    const openToolboxItem = useCallback(
      (type: string) => {
        if (
          layoutStore.activeToolboxItem === type &&
          layoutStore.controlsVisible
        ) {
          layoutStore.setControlsVisible(false);
          layoutStore.setActiveToolboxItem(null);
        } else {
          layoutStore.setActiveToolboxItem(type);
          layoutStore.setControlsVisible(true);
        }
      },
      [layoutStore]
    );

    return (
      <Paper
        sx={{
          zIndex: 201,
          width: "56px",
          py: 1.5,
          position: "absolute",
          top: "50%",
          transform: "translateY(-50%)",
          right: "10px",
          bgcolor: "grey.100",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        <MenuList type={controlType} openToolboxItem={openToolboxItem} />
      </Paper>
    );
  }
);

// 添加一个通用的 IconButton 包装组件
const ToolboxIconButton = observer(
  ({
    onClick,
    toolboxType,
    children,
  }: {
    onClick: () => void;
    toolboxType: string;
    children: React.ReactNode;
  }) => {
    const layoutStore = useLayoutStore();
    const isActive =
      layoutStore.activeToolboxItem === toolboxType &&
      layoutStore.controlsVisible;

    return (
      <IconButton
        onClick={onClick}
        sx={{
          bgcolor: isActive ? "action.selected" : "transparent",
        }}
      >
        {children}
      </IconButton>
    );
  }
);

// Create a base MenuList component to reduce duplication
const BaseMenuList = ({ children }: { children: React.ReactNode }) => (
  <Box sx={{ display: "flex", flexDirection: "column", alignItems: "center" }}>
    {children}
  </Box>
);

export default ControlList;
