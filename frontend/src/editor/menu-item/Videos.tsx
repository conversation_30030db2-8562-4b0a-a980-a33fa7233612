import SearchIcon from "@mui/icons-material/Search";
import {
  Alert,
  Box,
  Chip,
  CircularProgress,
  ImageList,
  ImageListItem,
  InputAdornment,
  Skeleton,
  Stack,
  TextField,
  Typography,
  Tab,
  Tabs,
} from "@mui/material";
import axios from "axios";
import React, { memo, useCallback, useEffect, useState, useMemo } from "react";
import HoverVideoPlayer from "react-hover-video-player";
import { StoreContext } from "../../store";
import { getUid } from "../../utils";
import { useLanguage } from "../../i18n/LanguageContext";
// ... existing imports ...
import ArrowBackIosNewIcon from "@mui/icons-material/ArrowBackIosNew";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import IconButton from "@mui/material/IconButton";
import { throttle } from "lodash";

// 定义视频源类型
type VideoSource = "pexels" | "pixabay";

// Pexels API相关配置
const PEXELS_API_KEY =
  "nF2P3Rmh2RKgwAihislOqfZHFRt0JpRVyDvQjgnLKfkj2KnpAgLG9TQ1";

// Pixabay API相关配置
const PIXABAY_API_KEY = "**********************************"; // 请替换为你的Pixabay API密钥
const PIXABAY_API_URL = "https://pixabay.com/api/videos/";

const PER_PAGE = 10;
const SKELETON_COUNT = 4; // 骨架屏数量

// 视频标签
const VIDEO_TAGS = {
  common: [
    "nature",
    "people",
    "business",
    "technology",
    "food",
    "travel",
    "sports",
    "education",
  ],
  pixabay: [
    "backgrounds",
    "animation",
    "fashion",
    "science",
    "health",
    "music",
    "places",
    "animals",
  ],
  pexels: [
    "aerial",
    "time lapse",
    "slow motion",
    "3d",
    "abstract",
    "urban",
    "vintage",
    "cinematic",
  ],
};

// Add TypeScript interfaces
interface Video {
  id: string;
  video_files: Array<{ link: string }>;
  duration: number;
  // add other video properties as needed
}

// Add new interface for loading state
interface LoadingState {
  [key: string]: boolean;
}

// 添加视频批次接口
interface VideoBatch {
  id: number; // 批次ID
  videos: Video[]; // 一批视频
}

// Update VideoItemProps to include loading state
interface VideoItemProps {
  video: Video;
  onVideoAdd: (video: Video, index: number) => void;
  index: number;
  isLoading: boolean;
}

// 格式化时长为 mm:ss - 移到组件外部避免重复创建
const formatDuration = (duration: number) => {
  const minutes = Math.floor(duration / 60);
  const seconds = Math.floor(duration % 60);
  return `${minutes}:${seconds.toString().padStart(2, "0")}`;
};

// Memoize the VideoItem component with better optimization
const VideoItem = memo(
  ({ video, onVideoAdd, index, isLoading }: VideoItemProps) => {
    const [isLoaded, setIsLoaded] = useState(false);

    // 缓存视频URL和时长显示
    const videoUrl = useMemo(
      () => video.video_files[0].link,
      [video.video_files]
    );
    const durationDisplay = useMemo(
      () => (video.duration ? formatDuration(video.duration) : null),
      [video.duration]
    );

    // 优化点击处理
    const handleClick = useCallback(() => {
      if (!isLoading) {
        onVideoAdd(video, index);
      }
    }, [isLoading, onVideoAdd, video, index]);

    // 优化加载完成处理
    const handleLoadedMetadata = useCallback(() => {
      setIsLoaded(true);
    }, []);

    return (
      <ImageListItem
        onClick={handleClick}
        sx={{
          cursor: isLoading ? "wait" : "pointer",
          position: "relative",
          transition: "all 0.3s ease",
          borderRadius: 2,
          overflow: "hidden",
          opacity: isLoaded ? 1 : 0.5,
          height: "100%",

          "& img, & video": {
            width: "100%",
            height: "100% !important",
            objectFit: "cover",
            borderRadius: 2,
            display: "block",
          },
          "&:hover": {
            transform: "translateY(-4px)",
          },
        }}
      >
        <Box sx={{ width: "100%", height: "100%", position: "relative" }}>
          <HoverVideoPlayer
            videoSrc={videoUrl}
            onLoadedMetadata={handleLoadedMetadata}
            loadingOverlay={
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  height: "100%",
                }}
              >
                <CircularProgress />
              </Box>
            }
          />
          {/* 显示时长 */}
          {durationDisplay && (
            <Box
              sx={{
                position: "absolute",
                bottom: 8,
                left: 8,
                backgroundColor: "rgba(0, 0, 0, 0.6)",
                color: "white",
                padding: "2px 6px",
                borderRadius: "4px",
                fontSize: "0.75rem",
              }}
            >
              {durationDisplay}
            </Box>
          )}
          {isLoading && (
            <Box
              sx={{
                position: "absolute",
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "rgba(0, 0, 0, 0.5)",
                borderRadius: 2,
              }}
            >
              <CircularProgress size={20} sx={{ color: "white" }} />
            </Box>
          )}
        </Box>
      </ImageListItem>
    );
  },
  // 自定义比较函数，只在必要时重新渲染
  (prevProps, nextProps) => {
    return (
      prevProps.video.id === nextProps.video.id &&
      prevProps.index === nextProps.index &&
      prevProps.isLoading === nextProps.isLoading
    );
  }
);

// 改进骨架屏组件 - 使用固定高度提高性能
const VideoSkeleton = memo(() => {
  return (
    <ImageListItem>
      <Skeleton
        variant="rectangular"
        sx={{
          width: "100%",
          height: 220, // 使用固定高度
          borderRadius: 2,
        }}
        animation="wave"
      />
    </ImageListItem>
  );
});

// 骨架屏列表组件
const SkeletonList = memo(() => (
  <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
    {Array.from(new Array(SKELETON_COUNT)).map((_, index) => (
      <VideoSkeleton key={`skeleton-${index}`} />
    ))}
  </ImageList>
));

export const Videos = () => {
  const store = React.useContext(StoreContext);
  const { t } = useLanguage();
  const [initialVideos, setInitialVideos] = useState<Video[]>([]); // 初始视频
  const [videoBatches, setVideoBatches] = useState<VideoBatch[]>([]); // 批次视频
  const [batchCounter, setBatchCounter] = useState(0); // 批次计数器
  const [loading, setLoading] = useState(false);
  const [batchLoading, setBatchLoading] = useState<number | null>(null); // 当前加载批次
  const [hasNextPage, setHasNextPage] = useState(true);
  const [page, setPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [debouncedQuery, setDebouncedQuery] = useState("");
  const [selectedTag, setSelectedTag] = useState("");
  const [initialLoading, setInitialLoading] = useState(true);
  // Add these new hooks at the top of the component:
  const tagsContainerRef = React.useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);
  const [loadingStates, setLoadingStates] = useState<LoadingState>({});
  const [error, setError] = useState<string | null>(null);
  // 添加视频源选择状态
  const [videoSource, setVideoSource] = useState<VideoSource>("pixabay"); // 默认使用Pixabay

  // 缓存当前视频源对应的标签
  const currentSourceTags = useMemo(() => {
    const commonTags = VIDEO_TAGS.common;
    const sourceTags =
      videoSource === "pixabay" ? VIDEO_TAGS.pixabay : VIDEO_TAGS.pexels;
    return [...commonTags, ...sourceTags];
  }, [videoSource]);

  // 缓存计算当前显示的视频总数
  const totalVideosCount = useMemo(() => {
    return (
      initialVideos.length +
      videoBatches.reduce((sum, batch) => sum + batch.videos.length, 0)
    );
  }, [initialVideos.length, videoBatches]);

  // 判断是否没有视频 - 使用缓存
  const hasNoVideos = useMemo(() => {
    return (
      !loading &&
      !initialLoading &&
      initialVideos.length === 0 &&
      videoBatches.length === 0
    );
  }, [loading, initialLoading, initialVideos.length, videoBatches.length]);

  // Add these new functions in the component:
  const checkScroll = useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      setCanScrollLeft(container.scrollLeft > 0);
      setCanScrollRight(
        container.scrollLeft < container.scrollWidth - container.clientWidth
      );
    }
  }, []);

  const handleScrollLeft = useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: -200, behavior: "smooth" });
    }
  }, []);

  const handleScrollRight = useCallback(() => {
    const container = tagsContainerRef.current;
    if (container) {
      container.scrollBy({ left: 200, behavior: "smooth" });
    }
  }, []);

  // Add this effect to monitor scroll position:
  useEffect(() => {
    const container = tagsContainerRef.current;
    if (container) {
      checkScroll();
      container.addEventListener("scroll", checkScroll);
      window.addEventListener("resize", checkScroll);
      return () => {
        container.removeEventListener("scroll", checkScroll);
        window.removeEventListener("resize", checkScroll);
      };
    }
  }, [checkScroll]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    setInitialVideos([]);
    setVideoBatches([]);
    setBatchCounter(0);
    setPage(1);
    setHasNextPage(true);
    setError(null);
    const searchTerm = selectedTag || debouncedQuery;
    fetchVideos(1, searchTerm);
  }, [debouncedQuery, selectedTag, videoSource]);

  // Throttle the scroll handler
  const throttledHandleScroll = useCallback(
    throttle((event: React.UIEvent<HTMLElement>) => {
      const target = event.target as HTMLElement;
      if (
        !loading &&
        hasNextPage &&
        target.scrollHeight - target.scrollTop <= target.clientHeight + 10
      ) {
        fetchVideos(page, selectedTag || debouncedQuery);
      }
    }, 200),
    [loading, hasNextPage, page, selectedTag, debouncedQuery]
  );

  // 将Pixabay视频转换为统一格式
  const mapPixabayVideosToCommonFormat = useCallback(
    (videos: any[]): Video[] => {
      return videos.map((video) => {
        // Pixabay返回多种分辨率，我们使用large作为主要视频
        const videoUrl =
          video.videos.large.url ||
          video.videos.medium.url ||
          video.videos.small.url;
        return {
          id: video.id.toString(),
          video_files: [{ link: videoUrl }],
          duration: video.duration || 0,
          // 添加其他需要的属性
        };
      });
    },
    []
  );

  // 优化 fetchVideos 使用批次加载，支持多源
  const fetchVideos = useCallback(
    async (currentPage: number, query: string) => {
      if (loading) return;
      setLoading(true);
      setError(null);

      // 设置批次加载状态
      if (currentPage > 1) {
        setBatchLoading(batchCounter + 1);
      }

      try {
        let response;
        let fetchedVideos: Video[];

        if (videoSource === "pexels") {
          // Pexels API
          const endpoint = query
            ? `https://api.pexels.com/videos/search?query=${query}&per_page=${PER_PAGE}&page=${currentPage}`
            : `https://api.pexels.com/videos/popular?per_page=${PER_PAGE}&page=${currentPage}`;

          response = await axios.get(endpoint, {
            headers: {
              Authorization: PEXELS_API_KEY,
            },
          });

          fetchedVideos = response.data.videos.map((video: any) => ({
            id: video.id,
            video_files: video.video_files,
            duration: video.duration,
            // 添加其他需要的属性
          }));
        } else {
          // Pixabay API
          const endpoint = `${PIXABAY_API_URL}?key=${PIXABAY_API_KEY}&per_page=${PER_PAGE}&page=${currentPage}`;
          const queryParam = query ? `&q=${encodeURIComponent(query)}` : "";

          response = await axios.get(`${endpoint}${queryParam}`);
          fetchedVideos = mapPixabayVideosToCommonFormat(response.data.hits);
        }

        // 根据页码决定是设置初始视频还是添加新批次
        if (currentPage === 1) {
          setInitialVideos(fetchedVideos);
          setVideoBatches([]);
          setBatchCounter(0);
        } else {
          // 创建新批次
          const newBatch: VideoBatch = {
            id: batchCounter + 1,
            videos: fetchedVideos,
          };
          setBatchCounter(batchCounter + 1);
          setVideoBatches((prev) => [...prev, newBatch]);
        }

        // 检查是否还有下一页
        if (videoSource === "pexels") {
          setHasNextPage(response.data.videos.length === PER_PAGE);
        } else {
          setHasNextPage(
            response.data.hits && response.data.hits.length === PER_PAGE
          );
        }

        setPage(currentPage + 1);

        // 如果没有找到视频，显示提示
        if (fetchedVideos.length === 0 && currentPage === 1) {
          setError(t("no_videos_found_try_another"));
        }
      } catch (error) {
        const sourceName = videoSource === "pexels" ? "Pexels" : "Pixabay";
        setError(t("loading_videos_failed").replace("{0}", sourceName));
        console.error(`Error fetching videos from ${videoSource}:`, error);
      } finally {
        setLoading(false);
        setInitialLoading(false);
        setBatchLoading(null);
      }
    },
    [loading, batchCounter, videoSource, t, mapPixabayVideosToCommonFormat]
  );

  const handleAddVideo = useCallback(
    async (src: string, index: number, videoMetadata?: any) => {
      const videoId = `video-${index}`;
      setLoadingStates((prev) => ({ ...prev, [videoId]: true }));
      let videoElement: HTMLVideoElement | null = null;

      try {
        store.addVideoResource(src);
        const id = getUid();

        videoElement = document.createElement("video");
        videoElement.src = src;
        videoElement.id = `video-${id}`;
        videoElement.style.display = "none";
        document.body.appendChild(videoElement);

        // Add timeout to prevent infinite loading
        await Promise.race([
          new Promise((resolve, reject) => {
            videoElement!.addEventListener("loadedmetadata", () => {
              store.addVideoElement(videoElement!, id, videoMetadata);
              resolve(true);
            });
            videoElement!.addEventListener("error", (e) =>
              reject(new Error(`Failed to load video: ${e.message}`))
            );
          }),
          new Promise((_, reject) =>
            setTimeout(
              () => reject(new Error(t("video_loading_timeout"))),
              30000
            )
          ),
        ]);
      } catch (error) {
        console.error("Error loading video:", error);
        // Optionally show error to user via a toast or other UI mechanism
        setError(
          error instanceof Error ? error.message : t("video_loading_failed")
        );
      } finally {
        // Clean up video element if loading failed
        if (videoElement && !videoElement.parentNode) {
          document.body.removeChild(videoElement);
        }
        setLoadingStates((prev) => {
          const newState = { ...prev };
          delete newState[videoId];
          return newState;
        });
      }
    },
    [store, setError, t]
  );

  const onVideoAdd = useCallback(
    (video: Video, index: number) => {
      const src = video.video_files[0].link;
      const videoMetadata = { name: `${videoSource} Video ${index + 1}` };
      handleAddVideo(src, index, videoMetadata);
    },
    [handleAddVideo, videoSource]
  );

  // 渲染分隔标题
  const renderBatchTitle = useCallback(
    (batchNumber: number) => (
      <Box
        sx={{
          height: "1px",
          bgcolor: "divider",
          my: 3,
          position: "relative",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: -10,
            left: "50%",
            transform: "translateX(-50%)",
            bgcolor: "grey.100",
            px: 2,
          }}
        ></Box>
      </Box>
    ),
    []
  );

  // 优化标签选择处理
  const handleTagClick = useCallback((tag: string) => {
    setSelectedTag((prev) => (prev === tag ? "" : tag));
    setSearchQuery("");
  }, []);

  // 优化视频源切换处理
  const handleVideoSourceChange = useCallback((_, newValue: VideoSource) => {
    setVideoSource(newValue);
    setError(null);
    setInitialVideos([]);
    setVideoBatches([]);
    setSelectedTag("");
    setSearchQuery("");
    setHasNextPage(true);
    setPage(1);
    setInitialLoading(true);
  }, []);

  // 优化搜索输入处理
  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearchQuery(e.target.value);
      setSelectedTag("");
    },
    []
  );

  return (
    <Box
      sx={{
        flex: 1,
        display: "flex",
        flexDirection: "column",
        bgcolor: "grey.100",
        borderRadius: 2,
        boxShadow: "0 2px 8px rgba(0,0,0,0.1)",
      }}
    >
      <Box
        sx={{
          height: 56,
          display: "flex",
          alignItems: "center",
          px: 3,
          flexShrink: 0,
          borderBottom: "1px solid",
          borderColor: "divider",
        }}
      >
        <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
          {t("video_library")}
        </Typography>
      </Box>

      {/* 添加Tab标签切换不同的视频源 */}
      <Tabs
        value={videoSource}
        onChange={handleVideoSourceChange}
        sx={{ borderBottom: 1, borderColor: "divider" }}
      >
        <Tab label="Pixabay" value="pixabay" />
        <Tab label="Pexels" value="pexels" />
      </Tabs>

      <Box sx={{ p: 2 }}>
        <TextField
          fullWidth
          size="small"
          placeholder={t("search_videos")}
          value={searchQuery}
          onChange={handleSearchChange}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon color="action" />
              </InputAdornment>
            ),
          }}
          sx={{
            "& .MuiOutlinedInput-root": {
              bgcolor: "background.paper",
              "&:hover": {
                "& > fieldset": { borderColor: "primary.main" },
              },
            },
          }}
        />
        <Box
          sx={{
            width: 250,
            position: "relative",
            mt: 2,
            "&:hover .scroll-button": {
              display: "flex",
            },
          }}
        >
          <Box
            sx={{
              display: "flex",
              overflowX: "auto",
              scrollBehavior: "smooth",
              "&::-webkit-scrollbar": { display: "none" },
              msOverflowStyle: "none",
              scrollbarWidth: "none",
              px: 1,
              borderRadius: 2,
            }}
            ref={tagsContainerRef}
          >
            <Stack direction="row" spacing={1}>
              {currentSourceTags.map((tag) => (
                <Chip
                  key={tag}
                  label={tag}
                  onClick={() => handleTagClick(tag)}
                  color={selectedTag === tag ? "primary" : "default"}
                  sx={{ whiteSpace: "nowrap" }}
                />
              ))}
            </Stack>
          </Box>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              left: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.5,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollLeft}
          >
            <ArrowBackIosNewIcon fontSize="small" />
          </IconButton>

          <IconButton
            className="scroll-button"
            sx={{
              position: "absolute",
              right: 0,
              top: "50%",
              transform: "translateY(-50%)",
              bgcolor: "background.paper",
              opacity: 0.5,
              "&:hover": { bgcolor: "background.paper", opacity: 1 },
              display: "none",
            }}
            size="small"
            onClick={handleScrollRight}
          >
            <ArrowForwardIosIcon fontSize="small" />
          </IconButton>
        </Box>
      </Box>

      {/* 错误消息显示 */}
      {error && !initialLoading && (
        <Alert severity="warning" sx={{ mx: 2, mb: 2 }}>
          {error}
        </Alert>
      )}

      <Box
        onScroll={throttledHandleScroll}
        sx={{
          flex: 1,
          bgcolor: "grey.100",
          overflow: "auto",
          px: 2,
          "&::-webkit-scrollbar": {
            width: "5px",
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "rgba(0, 0, 0, 0.2)",
            borderRadius: "5px",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.3)",
            },
          },
        }}
      >
        {hasNoVideos && !error && (
          <Alert severity="info" sx={{ mb: 2, mt: 2 }}>
            {t("no_videos_found")}
          </Alert>
        )}

        {/* 初始加载的骨架屏 */}
        {(initialLoading || (loading && page === 1)) && <SkeletonList />}

        {/* 初始视频列表 */}
        {initialVideos.length > 0 && (
          <ImageList
            variant="masonry"
            cols={2}
            gap={16}
            sx={{ margin: 0, mt: 2 }}
          >
            {initialVideos.map((video, index) => (
              <VideoItem
                key={video.id}
                video={video}
                onVideoAdd={onVideoAdd}
                index={index}
                isLoading={loadingStates[`video-${index}`] || false}
              />
            ))}
          </ImageList>
        )}

        {/* 批次视频列表 */}
        {videoBatches.map((batch) => (
          <Box key={`batch-${batch.id}`}>
            {renderBatchTitle(batch.id)}
            <ImageList variant="masonry" cols={2} gap={16} sx={{ margin: 0 }}>
              {batch.videos.map((video, index) => {
                const videoIndex =
                  index + initialVideos.length + (batch.id - 1) * PER_PAGE;
                return (
                  <VideoItem
                    key={`${video.id}-batch-${batch.id}`}
                    video={video}
                    onVideoAdd={onVideoAdd}
                    index={videoIndex}
                    isLoading={loadingStates[`video-${videoIndex}`] || false}
                  />
                );
              })}
            </ImageList>
          </Box>
        ))}

        {/* 显示当前正在加载的批次骨架屏 */}
        {batchLoading !== null && (
          <>
            {renderBatchTitle(batchLoading)}
            <SkeletonList />
          </>
        )}

        {/* 结束指示器（当没有更多加载时） */}
        {!hasNextPage && initialVideos.length > 0 && !loading && (
          <Box
            sx={{
              p: 2,
              textAlign: "center",
              borderTop: "1px solid",
              borderColor: "divider",
              mt: 2,
            }}
          >
            <Typography variant="caption" color="text.secondary">
              {t("displayed_all_results").replace(
                "{0}",
                totalVideosCount.toString()
              )}
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};
