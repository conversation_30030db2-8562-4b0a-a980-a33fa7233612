/**
 * 字幕textAlign对齐功能测试
 * 验证字幕在背景框内的文本对齐是否正确
 */

import { CaptionStyle } from "../types";

// 测试字幕textAlign对齐功能
export function testCaptionTextAlign(): void {
  console.log("开始测试字幕textAlign对齐功能...");

  const canvasWidth = 1920;
  const canvasHeight = 1080;
  const backgroundWidth = 400;
  const backgroundHeight = 60;

  const testCases: Array<{
    name: string;
    style: CaptionStyle;
  }> = [
    {
      name: "左对齐",
      style: {
        textAlign: "left",
        originX: "center",
        originY: "bottom",
        positionX: 0,
        positionY: 0,
        width: backgroundWidth,
        fontSize: 35,
        lineHeight: 1.2,
      } as CaptionStyle,
    },
    {
      name: "居中对齐",
      style: {
        textAlign: "center",
        originX: "center",
        originY: "bottom",
        positionX: 0,
        positionY: 0,
        width: backgroundWidth,
        fontSize: 35,
        lineHeight: 1.2,
      } as CaptionStyle,
    },
    {
      name: "右对齐",
      style: {
        textAlign: "right",
        originX: "center",
        originY: "bottom",
        positionX: 0,
        positionY: 0,
        width: backgroundWidth,
        fontSize: 35,
        lineHeight: 1.2,
      } as CaptionStyle,
    },
  ];

  testCases.forEach((testCase) => {
    console.log(`\n测试用例: ${testCase.name}`);

    // 模拟后端背景框位置计算（与assSubtitleUtils.ts保持一致）
    const backgroundX =
      (canvasWidth - backgroundWidth) / 2 + (testCase.style.positionX || 0);
    const backgroundY =
      canvasHeight -
      Math.round(canvasHeight * 0.02) -
      backgroundHeight +
      (testCase.style.positionY || 0);

    // 模拟后端文本位置计算（与assSubtitleUtils.ts保持一致）
    const textMarginX = 10;
    let textX: number;

    switch (testCase.style.textAlign) {
      case "left":
        textX = backgroundX + textMarginX;
        break;
      case "right":
        textX = backgroundX + backgroundWidth - textMarginX;
        break;
      case "center":
      default:
        textX = backgroundX + backgroundWidth / 2;
        break;
    }

    const textY = backgroundY + backgroundHeight / 2;

    console.log(`背景框位置: (${backgroundX}, ${backgroundY})`);
    console.log(`背景框尺寸: ${backgroundWidth} x ${backgroundHeight}`);
    console.log(`文本位置: (${Math.round(textX)}, ${Math.round(textY)})`);
    console.log(`文本对齐: ${testCase.style.textAlign}`);

    // 验证文本位置是否在背景框内
    const isInBounds =
      textX >= backgroundX &&
      textX <= backgroundX + backgroundWidth &&
      textY >= backgroundY &&
      textY <= backgroundY + backgroundHeight;

    console.log(
      `文本位置验证: ${isInBounds ? "✅ 在背景框内" : "❌ 超出背景框"}`
    );

    // 验证对齐方式是否正确
    let alignmentCorrect = false;
    const centerX = backgroundX + backgroundWidth / 2;
    const leftBoundary = backgroundX + textMarginX;
    const rightBoundary = backgroundX + backgroundWidth - textMarginX;

    switch (testCase.style.textAlign) {
      case "left":
        alignmentCorrect = Math.abs(textX - leftBoundary) < 1;
        break;
      case "center":
        alignmentCorrect = Math.abs(textX - centerX) < 1;
        break;
      case "right":
        alignmentCorrect = Math.abs(textX - rightBoundary) < 1;
        break;
    }

    console.log(
      `对齐方式验证: ${alignmentCorrect ? "✅ 对齐正确" : "❌ 对齐错误"}`
    );

    // 输出ASS对齐值
    let assAlignment = 5; // 默认中心对齐
    switch (testCase.style.textAlign) {
      case "left":
        assAlignment = 4; // 左中对齐
        break;
      case "right":
        assAlignment = 6; // 右中对齐
        break;
      case "center":
      default:
        assAlignment = 5; // 中心对齐
        break;
    }

    console.log(`ASS对齐值: \\an${assAlignment}`);
    console.log(
      `ASS位置标签: {\\an${assAlignment}\\pos(${Math.round(textX)},${Math.round(
        textY
      )})}`
    );
  });

  console.log("\n字幕textAlign对齐功能测试完成");
}

// 测试前端Canvas和后端ASS的textAlign一致性
export function testFrontendBackendTextAlignConsistency(): void {
  console.log("开始测试前端Canvas和后端ASS的textAlign一致性...");

  const testCases = [
    { textAlign: "left", expectedASSAlignment: 4 },
    { textAlign: "center", expectedASSAlignment: 5 },
    { textAlign: "right", expectedASSAlignment: 6 },
  ];

  testCases.forEach((testCase) => {
    console.log(`\n测试textAlign: ${testCase.textAlign}`);

    // 前端Fabric.js textAlign属性
    console.log(`前端Fabric.js textAlign: "${testCase.textAlign}"`);

    // 后端ASS对齐值
    console.log(`后端ASS对齐值: \\an${testCase.expectedASSAlignment}`);

    // 验证对齐值映射
    const alignmentMapping = {
      left: 4, // 左中对齐
      center: 5, // 中心对齐
      right: 6, // 右中对齐
    };

    const actualAlignment =
      alignmentMapping[testCase.textAlign as keyof typeof alignmentMapping];
    const isConsistent = actualAlignment === testCase.expectedASSAlignment;

    console.log(`对齐映射验证: ${isConsistent ? "✅ 一致" : "❌ 不一致"}`);

    if (!isConsistent) {
      console.log(
        `期望: \\an${testCase.expectedASSAlignment}, 实际: \\an${actualAlignment}`
      );
    }
  });

  console.log("\n前端Canvas和后端ASS的textAlign一致性测试完成");
}

// 测试字幕scale缩放功能
export function testCaptionScaleFeature(): void {
  console.log("开始测试字幕scale缩放功能...");

  const testCases = [
    { scaleX: 1, scaleY: 1, description: "无缩放" },
    { scaleX: 1.5, scaleY: 1.5, description: "1.5倍缩放" },
    { scaleX: 2, scaleY: 2, description: "2倍缩放" },
    { scaleX: 0.8, scaleY: 0.8, description: "0.8倍缩放" },
    { scaleX: 2, scaleY: 1, description: "水平2倍缩放" },
    { scaleX: 1, scaleY: 2, description: "垂直2倍缩放" },
  ];

  testCases.forEach((testCase) => {
    console.log(`\n测试用例: ${testCase.description}`);

    // 基础样式
    const baseStyle = {
      fontSize: 35,
      width: 400,
      strokeWidth: 2,
      charSpacing: 5,
      shadowOffsetX: 2,
      shadowOffsetY: 2,
      scaleX: testCase.scaleX,
      scaleY: testCase.scaleY,
    };

    // 计算缩放后的值
    const scaledFontSize = Math.round(baseStyle.fontSize * testCase.scaleY);
    const scaledWidth = baseStyle.width * testCase.scaleX;
    const scaledHeight = (baseStyle.fontSize * 1.2 + 20) * testCase.scaleY; // 简化的高度计算
    const scaledStrokeWidth = Math.round(
      baseStyle.strokeWidth * testCase.scaleX
    );
    const scaledCharSpacing = baseStyle.charSpacing * testCase.scaleX;

    console.log(
      `缩放因子: scaleX=${testCase.scaleX}, scaleY=${testCase.scaleY}`
    );
    console.log(`字体大小: ${baseStyle.fontSize} -> ${scaledFontSize}`);
    console.log(`背景框宽度: ${baseStyle.width} -> ${scaledWidth}`);
    console.log(
      `背景框高度: ${baseStyle.fontSize * 1.2 + 20} -> ${scaledHeight}`
    );
    console.log(`描边宽度: ${baseStyle.strokeWidth} -> ${scaledStrokeWidth}`);
    console.log(`字符间距: ${baseStyle.charSpacing} -> ${scaledCharSpacing}`);

    // 验证缩放计算
    const fontSizeCorrect =
      scaledFontSize === Math.round(baseStyle.fontSize * testCase.scaleY);
    const widthCorrect =
      Math.abs(scaledWidth - baseStyle.width * testCase.scaleX) < 0.01;
    const strokeCorrect =
      scaledStrokeWidth === Math.round(baseStyle.strokeWidth * testCase.scaleX);
    const charSpacingCorrect =
      Math.abs(scaledCharSpacing - baseStyle.charSpacing * testCase.scaleX) <
      0.01;

    console.log(`字体大小计算: ${fontSizeCorrect ? "✅ 正确" : "❌ 错误"}`);
    console.log(`背景框宽度计算: ${widthCorrect ? "✅ 正确" : "❌ 错误"}`);
    console.log(`描边宽度计算: ${strokeCorrect ? "✅ 正确" : "❌ 错误"}`);
    console.log(`字符间距计算: ${charSpacingCorrect ? "✅ 正确" : "❌ 错误"}`);

    // 验证前端Canvas和后端ASS的scale一致性
    console.log(
      `前端Canvas scaleX: ${testCase.scaleX}, scaleY: ${testCase.scaleY}`
    );
    console.log(
      `后端ASS应用: 字体${scaledFontSize}px, 背景框${scaledWidth}x${scaledHeight}, 描边${scaledStrokeWidth}px, 字符间距${scaledCharSpacing}`
    );

    const overallCorrect =
      fontSizeCorrect && widthCorrect && strokeCorrect && charSpacingCorrect;
    console.log(`整体验证: ${overallCorrect ? "✅ 通过" : "❌ 失败"}`);
  });

  console.log("\n字幕scale缩放功能测试完成");
}

// 测试前端Canvas和后端ASS的scale一致性
export function testFrontendBackendScaleConsistency(): void {
  console.log("开始测试前端Canvas和后端ASS的scale一致性...");

  const testCases = [
    { scaleX: 1.5, scaleY: 1.5, description: "1.5倍等比缩放" },
    { scaleX: 2, scaleY: 1, description: "水平2倍缩放" },
    { scaleX: 1, scaleY: 2, description: "垂直2倍缩放" },
    { scaleX: 0.8, scaleY: 1.2, description: "非等比缩放" },
  ];

  testCases.forEach((testCase) => {
    console.log(`\n测试用例: ${testCase.description}`);

    // 模拟前端Canvas的scale应用
    const frontendStyle = {
      fontSize: 35,
      width: 400,
      strokeWidth: 2,
      charSpacing: 5,
      scaleX: testCase.scaleX,
      scaleY: testCase.scaleY,
    };

    // 前端Canvas中的scale应用
    console.log(`前端Canvas属性:`);
    console.log(`  fabric.scaleX: ${frontendStyle.scaleX}`);
    console.log(`  fabric.scaleY: ${frontendStyle.scaleY}`);
    console.log(`  fabric.fontSize: ${frontendStyle.fontSize}`);
    console.log(`  fabric.width: ${frontendStyle.width}`);

    // 后端ASS中的scale应用（模拟assSubtitleUtils.ts的计算）
    const backendScaledFontSize = Math.round(
      frontendStyle.fontSize * testCase.scaleY
    );
    const backendScaledWidth = frontendStyle.width * testCase.scaleX;
    const backendScaledStrokeWidth = Math.round(
      frontendStyle.strokeWidth * testCase.scaleX
    );
    const backendScaledCharSpacing =
      frontendStyle.charSpacing * testCase.scaleX;

    console.log(`后端ASS计算结果:`);
    console.log(`  ASS字体大小: ${backendScaledFontSize}px`);
    console.log(`  ASS背景框宽度: ${backendScaledWidth}px`);
    console.log(`  ASS描边宽度: ${backendScaledStrokeWidth}px`);
    console.log(`  ASS字符间距: ${backendScaledCharSpacing}`);

    // 验证一致性
    const fontSizeConsistent =
      backendScaledFontSize ===
      Math.round(frontendStyle.fontSize * testCase.scaleY);
    const widthConsistent =
      Math.abs(backendScaledWidth - frontendStyle.width * testCase.scaleX) <
      0.01;
    const strokeConsistent =
      backendScaledStrokeWidth ===
      Math.round(frontendStyle.strokeWidth * testCase.scaleX);
    const charSpacingConsistent =
      Math.abs(
        backendScaledCharSpacing - frontendStyle.charSpacing * testCase.scaleX
      ) < 0.01;

    console.log(`一致性验证:`);
    console.log(`  字体大小: ${fontSizeConsistent ? "✅ 一致" : "❌ 不一致"}`);
    console.log(`  背景框宽度: ${widthConsistent ? "✅ 一致" : "❌ 不一致"}`);
    console.log(`  描边宽度: ${strokeConsistent ? "✅ 一致" : "❌ 不一致"}`);
    console.log(
      `  字符间距: ${charSpacingConsistent ? "✅ 一致" : "❌ 不一致"}`
    );

    const overallConsistent =
      fontSizeConsistent &&
      widthConsistent &&
      strokeConsistent &&
      charSpacingConsistent;
    console.log(`整体一致性: ${overallConsistent ? "✅ 通过" : "❌ 失败"}`);
  });

  console.log("\n前端Canvas和后端ASS的scale一致性测试完成");
}

// 如果直接运行此文件，执行测试
if (typeof window === "undefined") {
  testCaptionTextAlign();
  testFrontendBackendTextAlignConsistency();
  testCaptionScaleFeature();
  testFrontendBackendScaleConsistency();
}
